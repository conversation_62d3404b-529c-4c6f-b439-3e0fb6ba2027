import 'package:flutter/material.dart';

import '../../../../core/constants/assets_colors.dart';
import '../../../../core/constants/assets_fonts.dart';

/// شريط البحث المخصص للآفات والأمراض - مكون تشاركي
///
/// يوفر واجهة بحث موحدة مع إمكانيات متقدمة للتصفية
class PestDiseaseSearchBar extends StatelessWidget {
  /// متحكم النص
  final TextEditingController controller;

  /// نص التلميح
  final String hintText;

  /// دالة عند تغيير النص
  final ValueChanged<String>? onChanged;

  /// دالة عند الضغط على زر المسح
  final VoidCallback? onClear;

  /// دالة عند الضغط على زر الفلترة
  final VoidCallback? onFilter;

  /// عدد الفلاتر المطبقة
  final int appliedFiltersCount;

  /// إنشاء شريط البحث
  const PestDiseaseSearchBar({
    super.key,
    required this.controller,
    this.hintText = 'ابحث عن آفة أو مرض...',
    this.onChanged,
    this.onClear,
    this.onFilter,
    this.appliedFiltersCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة البحث
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Icon(
              Icons.search,
              color: AssetsColors.primary,
              size: 24,
            ),
          ),
          
          // حقل النص
          Expanded(
            child: TextField(
              controller: controller,
              onChanged: onChanged,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  color: Colors.grey[500],
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 16,
                ),
              ),
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
          
          // زر المسح
          if (controller.text.isNotEmpty)
            IconButton(
              onPressed: () {
                controller.clear();
                onClear?.call();
              },
              icon: Icon(
                Icons.clear,
                color: Colors.grey[600],
                size: 20,
              ),
            ),
          
          // زر الفلترة
          if (onFilter != null)
            Container(
              margin: const EdgeInsets.only(right: 8),
              child: Stack(
                children: [
                  IconButton(
                    onPressed: onFilter,
                    icon: Icon(
                      Icons.filter_list,
                      color: appliedFiltersCount > 0 
                          ? AssetsColors.primary 
                          : Colors.grey[600],
                      size: 24,
                    ),
                  ),
                  
                  // مؤشر عدد الفلاتر
                  if (appliedFiltersCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '$appliedFiltersCount',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontFamily: AssetsFonts.cairo,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
