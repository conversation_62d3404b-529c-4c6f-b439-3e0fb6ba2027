import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/headings.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/crops/disease_model.dart';
import '../../bloc/agricultural_crops/crops_cubit.dart';
import '../../widgets/shared/custom_loading_animation.dart';
import '../../../data/models/pests_diseases_data.dart';
import 'simple_diagnosis_page.dart';

/// صفحة الآفات والأمراض الزراعية المبسطة
///
/// تعرض هذه الصفحة جميع الآفات والأمراض التي تصيب المحاصيل الزراعية
/// مع واجهة مستخدم بسيطة وسهلة الاستخدام
class SimplePestsDiseasesPage extends StatefulWidget {
  /// إنشاء صفحة الآفات والأمراض المبسطة
  const SimplePestsDiseasesPage({super.key});

  @override
  State<SimplePestsDiseasesPage> createState() => _SimplePestsDiseasesPageState();
}

class _SimplePestsDiseasesPageState extends State<SimplePestsDiseasesPage>
    with TickerProviderStateMixin {
  /// متحكم البحث
  final TextEditingController _searchController = TextEditingController();

  /// متحكم التبويبات
  late TabController _tabController;

  /// قائمة الأمراض المفلترة
  List<Disease> _filteredDiseases = [];

  /// قائمة الأمراض المفضلة
  final List<Disease> _favoriteDiseases = [];

  /// حالة التحميل
  bool _isLoading = false;

  /// إحصائيات البحث
  int _totalResults = 0;

  /// البيانات الجديدة للآفات والأمراض
  List<Map<String, dynamic>> _allPestsData = [];
  List<Map<String, dynamic>> _filteredPestsData = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _searchController.addListener(_onSearchChanged);
    _loadPestsData();
  }

  /// تحميل بيانات الآفات والأمراض
  void _loadPestsData() {
    setState(() {
      _isLoading = true;
    });

    // تحميل البيانات من المصدر الجديد
    _allPestsData = PestsDiseasesData.getAllPestsAndDiseases();
    _filteredPestsData = _allPestsData;
    _totalResults = _allPestsData.length;

    setState(() {
      _isLoading = false;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  /// معالج تغيير البحث
  void _onSearchChanged() {
    _filterPests();
  }

  /// تصفية الآفات والأمراض
  void _filterPests() {
    setState(() {
      _filteredPestsData = _allPestsData.where((pest) {
        // تصفية حسب النص المدخل
        final searchText = _searchController.text.toLowerCase();
        final matchesSearch = searchText.isEmpty ||
            pest['name'].toString().toLowerCase().contains(searchText) ||
            pest['scientificName'].toString().toLowerCase().contains(searchText) ||
            pest['description'].toString().toLowerCase().contains(searchText);

        return matchesSearch;
      }).toList();

      _totalResults = _filteredPestsData.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: context.read<CropsCubit>(),
      child: Scaffold(
        appBar: _buildAppBar(context),
        body: _buildBody(context),
        floatingActionButton: FloatingActionButton(
          onPressed: () => _navigateToSimpleDiagnosis(),
          backgroundColor: AssetsColors.primary,
          child: const Icon(Icons.camera_alt, color: Colors.white),
          tooltip: 'تشخيص بالصورة',
        ),
      ),
    );
  }

  /// بناء شريط التطبيق المبسط
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Headings.pestsAndDiseases,
            style: TextStyles.of(context).headlineMedium(
              fontSize: 18,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: AssetsColors.kWhite,
            ),
          ),
          if (_totalResults > 0)
            Text(
              '$_totalResults نتيجة',
              style: TextStyle(
                fontSize: 12,
                fontFamily: AssetsFonts.cairo,
                color: Colors.white70,
              ),
            ),
        ],
      ),
      backgroundColor: AssetsColors.primary,
      elevation: 0,
      actions: [
        // زر المفضلة
        Stack(
          children: [
            IconButton(
              icon: Icon(Icons.favorite, color: Colors.white),
              onPressed: () => _showFavorites(context),
              tooltip: 'المفضلة',
            ),
            if (_favoriteDiseases.isNotEmpty)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    '${_favoriteDiseases.length}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: TextStyle(
          fontFamily: AssetsFonts.cairo,
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelStyle: TextStyle(
          fontFamily: AssetsFonts.cairo,
        ),
        tabs: const [
          Tab(text: 'جميع الآفات والأمراض'),
          Tab(text: 'المفضلة'),
        ],
      ),
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody(BuildContext context) {
    return Column(
      children: [
        // شريط البحث
        _buildSearchBar(context),

        // محتوى التبويبات
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildAllPestsTab(context),
              _buildFavoritesTab(context),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'ابحث عن مرض أو آفة...',
          hintStyle: TextStyle(
            fontFamily: AssetsFonts.cairo,
            color: AssetsColors.kGrey70,
          ),
          prefixIcon: Icon(Icons.search, color: AssetsColors.primary),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear, color: AssetsColors.kGrey70),
                  onPressed: () {
                    _searchController.clear();
                    _onSearchChanged();
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        style: TextStyle(
          fontFamily: AssetsFonts.cairo,
          color: AssetsColors.kGrey100,
        ),
        onChanged: (value) => _onSearchChanged(),
      ),
    );
  }

  /// بناء تبويب جميع الآفات والأمراض
  Widget _buildAllPestsTab(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CustomLoadingAnimation());
    }

    if (_filteredPestsData.isEmpty) {
      return _buildEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadPestsData();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredPestsData.length,
        itemBuilder: (context, index) {
          final pest = _filteredPestsData[index];
          return _buildPestCard(context, pest);
        },
      ),
    );
  }

  /// بناء بطاقة المرض
  Widget _buildDiseaseCard(BuildContext context, Disease disease) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showDiseaseDetails(context, disease),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة المرض
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AssetsColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.local_hospital,
                  color: AssetsColors.primary,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              // معلومات المرض
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      disease.name,
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      disease.details,
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // زر المفضلة
              IconButton(
                onPressed: () => _toggleFavorite(disease),
                icon: Icon(
                  _favoriteDiseases.contains(disease)
                      ? Icons.favorite
                      : Icons.favorite_border,
                  color: _favoriteDiseases.contains(disease)
                      ? Colors.red
                      : Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد نتائج',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب البحث بكلمات مختلفة',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة المفضلة الفارغة
  Widget _buildEmptyFavoritesState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مفضلة',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أضف آفات وأمراض إلى المفضلة لسهولة الوصول إليها',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء تبويب المفضلة
  Widget _buildFavoritesTab(BuildContext context) {
    if (_favoriteDiseases.isEmpty) {
      return _buildEmptyFavoritesState(context);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _favoriteDiseases.length,
      itemBuilder: (context, index) {
        final disease = _favoriteDiseases[index];
        return _buildDiseaseCard(context, disease);
      },
    );
  }

  /// بناء بطاقة الآفة/المرض
  Widget _buildPestCard(BuildContext context, Map<String, dynamic> pest) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showPestDetails(context, pest),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة الآفة/المرض
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: _getSeverityColor(pest['severity']).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getPestIcon(pest['category']),
                  color: _getSeverityColor(pest['severity']),
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              // معلومات الآفة/المرض
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      pest['name'] ?? 'غير محدد',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      pest['scientificName'] ?? '',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getSeverityColor(pest['severity']).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        pest['severity'] ?? 'غير محدد',
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 12,
                          color: _getSeverityColor(pest['severity']),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // سهم للتفاصيل
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على لون مستوى الخطورة
  Color _getSeverityColor(String? severity) {
    switch (severity) {
      case 'عالي جداً':
      case 'عالي':
        return Colors.red;
      case 'متوسط':
        return Colors.orange;
      case 'منخفض':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة الآفة/المرض
  IconData _getPestIcon(String? category) {
    switch (category) {
      case 'آفات':
      case 'حشرات':
        return Icons.bug_report;
      case 'أمراض':
      case 'فطريات':
        return Icons.local_hospital;
      case 'فيروسات':
        return Icons.coronavirus;
      default:
        return Icons.eco;
    }
  }

  /// التنقل إلى صفحة التشخيص البسيط
  void _navigateToSimpleDiagnosis() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SimpleDiagnosisPage()),
    );
  }

  /// عرض تفاصيل الآفة/المرض
  void _showPestDetails(BuildContext context, Map<String, dynamic> pest) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // رأس الصفحة
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: _getSeverityColor(pest['severity']),
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Row(
                children: [
                  Icon(
                    _getPestIcon(pest['category']),
                    color: Colors.white,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      pest['name'] ?? 'غير محدد',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            // محتوى التفاصيل
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (pest['scientificName'] != null) ...[
                      Text(
                        'الاسم العلمي:',
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        pest['scientificName'],
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 14,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    Text(
                      'الوصف:',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      pest['description'] ?? 'لا يوجد وصف متاح',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 14,
                        height: 1.5,
                      ),
                    ),
                    if (pest['treatment'] != null) ...[
                      const SizedBox(height: 16),
                      Text(
                        'العلاج:',
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          pest['treatment'],
                          style: TextStyle(
                            fontFamily: AssetsFonts.cairo,
                            fontSize: 14,
                            height: 1.5,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض تفاصيل المرض
  void _showDiseaseDetails(BuildContext context, Disease disease) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // رأس الصفحة
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AssetsColors.primary,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.local_hospital,
                    color: Colors.white,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      disease.name,
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            // محتوى التفاصيل
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'التفاصيل:',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      disease.details,
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 14,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// تبديل حالة المفضلة
  void _toggleFavorite(Disease disease) {
    setState(() {
      if (_favoriteDiseases.contains(disease)) {
        _favoriteDiseases.remove(disease);
      } else {
        _favoriteDiseases.add(disease);
      }
    });
  }

  /// عرض المفضلة
  void _showFavorites(BuildContext context) {
    _tabController.animateTo(1);
  }
}