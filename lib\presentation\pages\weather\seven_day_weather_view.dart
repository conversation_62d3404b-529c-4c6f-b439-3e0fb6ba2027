import 'package:agriculture/imports.dart';
import 'package:jiffy/jiffy.dart';

import '../../../data/models/weather/weather_model.dart';

import '../../widgets/weather/weather_loading_widget.dart';

class DayWeatherView extends StatelessWidget {
  const DayWeatherView({super.key});

  @override
  Widget build(BuildContext context) {
    // تحميل بيانات الطقس عند بناء الويدجت
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LoggerService.info(
        'بدء تحميل بيانات الطقس من صفحة توقعات الأسبوع',
        tag: 'DayWeatherView',
      );
      try {
        context.read<WeatherCubit>().loadAllWeatherData();
      } catch (e) {
        LoggerService.error(
          'خطأ في تحميل بيانات الطقس',
          error: e,
          tag: 'DayWeatherView',
        );
      }
    });
    return Scaffold(
      backgroundColor: AssetsColors.wether2,
      body: <PERSON><PERSON><PERSON>(
        child: BlocConsumer<WeatherCubit, WeatherState>(
          listener: (BuildContext context, WeatherState state) {
            if (state is WeatherError) {
              // استخدام حوار الخطأ المتحرك بدلاً من SnackBar
              AnimatedErrorDialog.show(
                context: context,
                title: 'خطأ في بيانات الطقس',
                message: state.message,
                onRetry:
                    () => context.read<WeatherCubit>().refreshWeatherData(),
              );
            }
          },
          builder: (context, state) {
            // var weeklyCubit = WeatherGetCubit.get(context).weekly;
            //
            // final time = DateTime.parse(weeklyCubit.daily.time[0]).dayOfWeek;
            if (state is WeatherInitial || state is WeatherLoading) {
              // استخدام ويدجت التحميل المحسن
              return const WeatherLoadingWidget(
                loadingType: WeatherLoadingType.full,
              );
            }
            if (state is WeatherError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.cloud_off,
                      size: 64,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'تعذر تحميل بيانات الطقس',
                      style: TextStyles.of(context).bodyLarge(
                        fontSize: 18,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      style: TextStyles.of(context).bodyMedium(
                        fontSize: 14,
                        fontFamily: AssetsFonts.cairo,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed:
                          () =>
                              context.read<WeatherCubit>().refreshWeatherData(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AssetsColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }

            if (state is WeatherLoaded) {
              var weeklyCubit = state.weeklyForecast;
              WeatherModel weather = state.currentWeather;
              return Padding(
                padding: const EdgeInsets.only(left: 18.0, right: 18),
                // إضافة ميزة السحب للتحديث
                child: RefreshIndicator(
                  onRefresh:
                      () => context.read<WeatherCubit>().refreshWeatherData(),
                  color: AssetsColors.primary,
                  backgroundColor: Colors.white,
                  displacement: 40,
                  strokeWidth: 3,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 10,
                      // mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconAppBar(
                              backgroundColor: AssetsColors.primary,
                              colorIcon: AssetsColors.kWhite,
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              icon: Icons.arrow_back_ios_new,
                            ),
                            Text(
                              'توقع 7 ايام',
                              style: TextStyles.of(context).bodyLarge(
                                fontSize: 17,
                                fontFamily: AssetsFonts.cairo,
                                fontWeight: FontWeight.bold,
                                color: AssetsColors.kWhite,
                              ),
                            ),
                            IconAppBar(
                              backgroundColor: AssetsColors.primary,
                              colorIcon: AssetsColors.kWhite,
                              onTap: () {
                                // استخدام دالة refreshWeatherData لجلب بيانات محدثة من الخادم دائمًا
                                context
                                    .read<WeatherCubit>()
                                    .refreshWeatherData();
                              },
                              icon: Icons.refresh,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Container(
                          clipBehavior: Clip.antiAliasWithSaveLayer,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: AssetsColors.primary,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Column(
                            children: [
                              SizedBox(height: 20),
                              // عرض اسم اليوم
                              Text(
                                Jiffy.now().format(pattern: 'EEEE'),
                                style: TextStyles.of(context).bodyLarge(
                                  fontSize: 22,
                                  fontFamily: AssetsFonts.cairo,
                                  fontWeight: FontWeight.bold,
                                  color: AssetsColors.kWhite,
                                ),
                              ),
                              SizedBox(height: 10),

                              // عرض اسم المدينة مع الأيقونة
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    color: AssetsColors.kWhite,
                                    size: 25,
                                  ),
                                  SizedBox(width: 5),
                                  Text(
                                    weather.name != null
                                        ? weather.name.toString()
                                        : 'غير معروف',
                                    style: TextStyles.of(context).bodyLarge(
                                      fontSize: 18,
                                      fontFamily: AssetsFonts.cairo,
                                      color: AssetsColors.kWhite,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 20),

                              // استخدام مكون StackHomeWeather لعرض درجة الحرارة والأيقونة
                              StackHomeWeather(
                                numberWeather:
                                    weeklyCubit.daily!.temperature2mMax![0]
                                        .round()
                                        .toString(),
                                color: AssetsColors.kWhite,
                                imageAssets:
                                    'assets/icons/${weather.weather![0].icon!.replaceAll('n', 'd')}.png',
                              ),

                              // عرض وصف الطقس
                              // Text(
                              //   weather.weather != null &&
                              //           weather.weather!.isNotEmpty &&
                              //           weather.weather![0].description != null
                              //       ? weather.weather![0].description.toString()
                              //       : '',
                              //   style: TextStyles.of(context).bodyLarge(
                              //     fontSize: 16,
                              //     fontFamily: AssetsFonts.messiri,
                              //     fontWeight: FontWeight.bold,
                              //     color: AssetsColors.kWhite,
                              //   ),
                              // ),
                             
                              SizedBox(height: 20),
                              RowWeather(
                                temperature:
                                    weather.main?.temp != null
                                        ? weather.main!.temp!.round().toString()
                                        : '0',
                                humidity:
                                    weather.main?.humidity != null
                                        ? weather.main!.humidity.toString()
                                        : '0',
                                windSpeed:
                                    weather.wind?.speed != null
                                        ? weather.wind!.speed!.toString()
                                        : '0',
                                color: AssetsColors.primary,
                              ),
                              SizedBox(height: 20),
                            ],
                          ),
                        ),
                        EnhancedWeeklyForecastView(),
                        SizedBox(height: 20),
                        // معلومات الطقس الزراعية
                        WeatherAgriculturalInfo(
                          weather: weather,
                          backgroundColor: AssetsColors.primary,
                        ),
                        SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              );
            }
            return const Center(child: Text('حدث خطأ غير متوقع'));
          },
        ),
      ),
    );
  }
}
