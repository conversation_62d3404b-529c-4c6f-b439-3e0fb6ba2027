import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/data/models/education/course_model.dart';
import 'package:agriculture/presentation/widgets/shared/cachd_net_image.dart';
import 'package:flutter/material.dart';

import '../../../core/constants/text_styles.dart';

/// بطاقة الدورة التدريبية
///
/// تعرض هذه البطاقة معلومات الدورة التدريبية
class CourseCard extends StatelessWidget {
  /// نموذج الدورة التدريبية
  final CourseModel course;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback onTap;

  /// إنشاء بطاقة الدورة التدريبية
  ///
  /// المعلمات:
  /// - [course]: نموذج الدورة التدريبية
  /// - [onTap]: دالة يتم استدعاؤها عند النقر على البطاقة
  const CourseCard({super.key, required this.course, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        decoration: BoxDecoration(
          color: AssetsColors.kWhite,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AssetsColors.primary.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الدورة
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              child: SizedBox(
                width: double.infinity,
                height: 150,
                child: CachedNetImage(
                  imageUrl: course.imageUrl,
                  fit: BoxFit.cover,
                ),
              ),
            ),

            // معلومات الدورة
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5,vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الدورة
                  Text(
                    course.title,
                    style: TextStyles.of(context).headlineMedium(
                      fontSize: 16,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                      color: AssetsColors.primary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // وصف الدورة
                  Text(
                    course.description,
                    style: TextStyles.of(context).bodyMedium(
                      fontSize: 14,
                      fontFamily: AssetsFonts.cairo,
                      color: AssetsColors.kGrey100,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 16),

                  // معلومات إضافية
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // المستوى
                      _buildInfoItem(
                        context,
                        Icons.signal_cellular_alt,
                        course.level,
                      ),

                      // المدة
                      _buildInfoItem(
                        context,
                        Icons.access_time,
                        _formatDuration(course.durationMinutes),
                      ),

                      // عدد الدروس
                      _buildInfoItem(
                        context,
                        Icons.video_library,
                        '${course.lessonsCount} درس',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر معلومات
  Widget _buildInfoItem(BuildContext context, IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AssetsColors.primary.withOpacity(0.7)),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyles.of(context).bodySmall(
            fontSize: 12,
            fontFamily: AssetsFonts.cairo,
            color: AssetsColors.kGrey100,
          ),
        ),
      ],
    );
  }

  /// تنسيق المدة
  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes دقيقة';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours ساعة';
      } else {
        return '$hours ساعة و $remainingMinutes دقيقة';
      }
    }
  }
}
