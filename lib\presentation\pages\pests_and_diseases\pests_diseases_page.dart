import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/headings.dart';
import '../../../core/constants/text_styles.dart';
import '../../bloc/pests_diseases/pests_diseases_cubit.dart';
import '../../bloc/pests_diseases/pests_diseases_state.dart';
import '../../widgets/pests_and_diseases/shared/pest_disease_card.dart';
import '../../widgets/pests_and_diseases/shared/search_bar_widget.dart';
import '../../widgets/pests_and_diseases/shared/filter_section.dart';
import '../../widgets/shared/custom_loading_animation.dart';

/// صفحة إدارة صحة المحاصيل الزراعية المحسنة
///
/// تعرض هذه الصفحة جميع الآفات والأمراض مع إمكانيات بحث وتصفية متقدمة
/// باستخدام Cubit لإدارة الحالة وفقاً لمبادئ Clean Architecture
class PestsDiseasesPage extends StatelessWidget {
  /// إنشاء صفحة إدارة صحة المحاصيل
  const PestsDiseasesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PestsDiseasesCubit()..loadPestsAndDiseases(),
      child: const _PestsDiseasesView(),
    );
  }
}

/// عرض صفحة الآفات والأمراض
class _PestsDiseasesView extends StatefulWidget {
  const _PestsDiseasesView();

  @override
  State<_PestsDiseasesView> createState() => _PestsDiseasesViewState();
}

class _PestsDiseasesViewState extends State<_PestsDiseasesView>
    with TickerProviderStateMixin {
  /// متحكم البحث
  final TextEditingController _searchController = TextEditingController();

  /// متحكم التبويبات
  late TabController _tabController;

  /// هل قسم الفلترة مفتوح
  bool _isFilterSectionOpen = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  /// معالج تغيير البحث
  void _onSearchChanged() {
    context.read<PestsDiseasesCubit>().searchPestsAndDiseases(_searchController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: _buildBody(context),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: BlocBuilder<PestsDiseasesCubit, PestsDiseasesState>(
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                Headings.pestsAndDiseases,
                style: TextStyles.of(context).headlineMedium(
                  fontSize: 18,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.kWhite,
                ),
              ),
              if (state is PestsDiseasesLoaded)
                Text(
                  '${state.filteredPestsAndDiseases.length} نتيجة',
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: AssetsFonts.cairo,
                    color: Colors.white70,
                  ),
                ),
            ],
          );
        },
      ),
      backgroundColor: AssetsColors.primary,
      elevation: 0,
      actions: [
        // زر المفضلة
        BlocBuilder<PestsDiseasesCubit, PestsDiseasesState>(
          builder: (context, state) {
            final favoritesCount = state is PestsDiseasesLoaded
                ? state.favorites.length
                : 0;

            return Stack(
              children: [
                IconButton(
                  icon: Icon(Icons.favorite, color: Colors.white),
                  onPressed: () => _tabController.animateTo(1),
                  tooltip: 'المفضلة',
                ),
                if (favoritesCount > 0)
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '$favoritesCount',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontFamily: AssetsFonts.cairo,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: TextStyle(
          fontFamily: AssetsFonts.cairo,
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelStyle: TextStyle(
          fontFamily: AssetsFonts.cairo,
        ),
        tabs: const [
          Tab(text: 'جميع الآفات والأمراض'),
          Tab(text: 'المفضلة'),
        ],
      ),
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody(BuildContext context) {
    return Column(
      children: [
        // شريط البحث
        _buildSearchSection(context),

        // قسم الفلترة (اختياري)
        if (_isFilterSectionOpen) _buildFilterSection(context),

        // محتوى التبويبات
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildAllPestsTab(context),
              _buildFavoritesTab(context),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء قسم البحث
  Widget _buildSearchSection(BuildContext context) {
    return BlocBuilder<PestsDiseasesCubit, PestsDiseasesState>(
      builder: (context, state) {
        final appliedFiltersCount = state is PestsDiseasesLoaded
            ? state.selectedCategories.length + state.selectedSeverityLevels.length
            : 0;

        return PestDiseaseSearchBar(
          controller: _searchController,
          onChanged: (value) => _onSearchChanged(),
          onClear: () => _onSearchChanged(),
          onFilter: () {
            setState(() {
              _isFilterSectionOpen = !_isFilterSectionOpen;
            });
          },
          appliedFiltersCount: appliedFiltersCount,
        );
      },
    );
  }

  /// بناء قسم الفلترة
  Widget _buildFilterSection(BuildContext context) {
    return BlocBuilder<PestsDiseasesCubit, PestsDiseasesState>(
      builder: (context, state) {
        if (state is! PestsDiseasesLoaded) return const SizedBox.shrink();

        final cubit = context.read<PestsDiseasesCubit>();

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: PestDiseaseFilterSection(
            categories: cubit.availableCategories,
            severityLevels: cubit.availableSeverityLevels,
            selectedCategories: state.selectedCategories,
            selectedSeverityLevels: state.selectedSeverityLevels,
            onCategoryChanged: (category) => cubit.toggleCategoryFilter(category),
            onSeverityChanged: (severity) => cubit.toggleSeverityFilter(severity),
            onClearAll: () {
              cubit.clearAllFilters();
              _searchController.clear();
            },
          ),
        );
      },
    );
  }

  /// بناء تبويب جميع الآفات والأمراض
  Widget _buildAllPestsTab(BuildContext context) {
    return BlocBuilder<PestsDiseasesCubit, PestsDiseasesState>(
      builder: (context, state) {
        if (state is PestsDiseasesLoading) {
          return const Center(child: CustomLoadingAnimation());
        }

        if (state is PestsDiseasesError) {
          return _buildErrorState(context, state.message);
        }

        if (state is PestsDiseasesLoaded) {
          if (state.filteredPestsAndDiseases.isEmpty) {
            return _buildEmptyState(context);
          }

          return RefreshIndicator(
            onRefresh: () async {
              context.read<PestsDiseasesCubit>().loadPestsAndDiseases();
            },
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: state.filteredPestsAndDiseases.length,
              itemBuilder: (context, index) {
                final pest = state.filteredPestsAndDiseases[index];
                return _buildPestCard(context, pest, state.favorites);
              },
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  /// بناء تبويب المفضلة
  Widget _buildFavoritesTab(BuildContext context) {
    return BlocBuilder<PestsDiseasesCubit, PestsDiseasesState>(
      builder: (context, state) {
        if (state is PestsDiseasesLoaded) {
          final favorites = context.read<PestsDiseasesCubit>().getFavorites();

          if (favorites.isEmpty) {
            return _buildEmptyFavoritesState(context);
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: favorites.length,
            itemBuilder: (context, index) {
              final pest = favorites[index];
              return _buildPestCard(context, pest, state.favorites);
            },
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  /// بناء بطاقة الآفة/المرض
  Widget _buildPestCard(BuildContext context, Map<String, dynamic> pest, List<String> favorites) {
    final pestId = pest['id']?.toString() ?? '';
    final isFavorite = favorites.contains(pestId);

    return PestDiseaseCard(
      name: pest['name'] ?? 'غير محدد',
      scientificName: pest['scientificName'],
      description: pest['description'] ?? 'لا يوجد وصف متاح',
      severity: pest['severity'] ?? 'غير محدد',
      category: pest['category'] ?? 'غير محدد',
      isFavorite: isFavorite,
      onTap: () => _showPestDetails(context, pest),
      onFavoriteToggle: () {
        context.read<PestsDiseasesCubit>().toggleFavorite(pestId);
      },
    );
  }

  /// عرض تفاصيل الآفة/المرض
  void _showPestDetails(BuildContext context, Map<String, dynamic> pest) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildPestDetailsSheet(context, pest),
    );
  }

  /// بناء صفحة تفاصيل الآفة/المرض
  Widget _buildPestDetailsSheet(BuildContext context, Map<String, dynamic> pest) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // رأس الصفحة
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AssetsColors.primary,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Row(
              children: [
                Icon(
                  _getPestIcon(pest['category']),
                  color: Colors.white,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    pest['name'] ?? 'غير محدد',
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          // محتوى التفاصيل
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (pest['scientificName'] != null) ...[
                    _buildDetailSection('الاسم العلمي:', pest['scientificName']),
                    const SizedBox(height: 16),
                  ],
                  _buildDetailSection('الوصف:', pest['description'] ?? 'لا يوجد وصف متاح'),
                  if (pest['treatment'] != null) ...[
                    const SizedBox(height: 16),
                    _buildTreatmentSection(pest['treatment']),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم التفاصيل
  Widget _buildDetailSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          content,
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontSize: 14,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  /// بناء قسم العلاج
  Widget _buildTreatmentSection(String treatment) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'العلاج:',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            treatment,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد نتائج',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب البحث بكلمات مختلفة أو تعديل الفلاتر',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء حالة المفضلة الفارغة
  Widget _buildEmptyFavoritesState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مفضلة',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أضف آفات وأمراض إلى المفضلة لسهولة الوصول إليها',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<PestsDiseasesCubit>().loadPestsAndDiseases();
            },
            child: Text(
              'إعادة المحاولة',
              style: TextStyle(fontFamily: AssetsFonts.cairo),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر الإجراء العائم
  Widget _buildFloatingActionButton(BuildContext context) {
    return FloatingActionButton(
      onPressed: () {
        // يمكن إضافة صفحة تشخيص هنا لاحقاً
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'ميزة التشخيص قيد التطوير',
              style: TextStyle(fontFamily: AssetsFonts.cairo),
            ),
          ),
        );
      },
      backgroundColor: AssetsColors.primary,
      tooltip: 'تشخيص بالصورة',
      child: const Icon(Icons.camera_alt, color: Colors.white),
    );
  }

  /// الحصول على أيقونة نوع الآفة/المرض
  IconData _getPestIcon(String? category) {
    switch (category) {
      case 'آفات':
      case 'حشرات':
        return Icons.bug_report;
      case 'أمراض':
      case 'فطريات':
        return Icons.local_hospital;
      case 'فيروسات':
        return Icons.coronavirus;
      default:
        return Icons.eco;
    }
  }
}