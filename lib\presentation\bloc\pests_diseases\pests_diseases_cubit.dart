import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/pests_diseases_data.dart';
import 'pests_diseases_state.dart';

/// Cubit إدارة الآفات والأمراض
///
/// يدير حالة واجهة الآفات والأمراض ويوفر جميع العمليات المطلوبة
/// وفقاً لمبادئ Clean Architecture
class PestsDiseasesCubit extends Cubit<PestsDiseasesState> {
  /// إنشاء Cubit الآفات والأمراض
  PestsDiseasesCubit() : super(const PestsDiseasesInitial());

  /// قائمة الفئات المتاحة
  final List<String> availableCategories = [
    'آفات',
    'أمراض',
    'فطريات',
    'فيروسات',
    'حشرات',
  ];

  /// قائمة مستويات الخطورة المتاحة
  final List<String> availableSeverityLevels = [
    'منخفض',
    'متوسط',
    'عالي',
    'عالي جداً',
  ];

  /// تحميل بيانات الآفات والأمراض
  Future<void> loadPestsAndDiseases() async {
    try {
      emit(const PestsDiseasesLoading());

      // تحميل البيانات من المصدر
      final allData = PestsDiseasesData.getAllPestsAndDiseases();

      // إنشاء حالة النجاح
      emit(PestsDiseasesLoaded(
        allPestsAndDiseases: allData,
        filteredPestsAndDiseases: allData,
        favorites: [],
      ));
    } catch (e) {
      emit(PestsDiseasesError(message: 'خطأ في تحميل البيانات: $e'));
    }
  }

  /// البحث في الآفات والأمراض
  void searchPestsAndDiseases(String query) {
    final currentState = state;
    if (currentState is! PestsDiseasesLoaded) return;

    final filteredData = _applyFilters(
      data: currentState.allPestsAndDiseases,
      searchQuery: query,
      selectedCategories: currentState.selectedCategories,
      selectedSeverityLevels: currentState.selectedSeverityLevels,
    );

    emit(currentState.copyWith(
      searchQuery: query,
      filteredPestsAndDiseases: filteredData,
    ));
  }

  /// تطبيق فلتر الفئة
  void toggleCategoryFilter(String category) {
    final currentState = state;
    if (currentState is! PestsDiseasesLoaded) return;

    final selectedCategories = List<String>.from(currentState.selectedCategories);
    
    if (selectedCategories.contains(category)) {
      selectedCategories.remove(category);
    } else {
      selectedCategories.add(category);
    }

    final filteredData = _applyFilters(
      data: currentState.allPestsAndDiseases,
      searchQuery: currentState.searchQuery,
      selectedCategories: selectedCategories,
      selectedSeverityLevels: currentState.selectedSeverityLevels,
    );

    emit(currentState.copyWith(
      selectedCategories: selectedCategories,
      filteredPestsAndDiseases: filteredData,
    ));
  }

  /// تطبيق فلتر مستوى الخطورة
  void toggleSeverityFilter(String severity) {
    final currentState = state;
    if (currentState is! PestsDiseasesLoaded) return;

    final selectedSeverityLevels = List<String>.from(currentState.selectedSeverityLevels);
    
    if (selectedSeverityLevels.contains(severity)) {
      selectedSeverityLevels.remove(severity);
    } else {
      selectedSeverityLevels.add(severity);
    }

    final filteredData = _applyFilters(
      data: currentState.allPestsAndDiseases,
      searchQuery: currentState.searchQuery,
      selectedCategories: currentState.selectedCategories,
      selectedSeverityLevels: selectedSeverityLevels,
    );

    emit(currentState.copyWith(
      selectedSeverityLevels: selectedSeverityLevels,
      filteredPestsAndDiseases: filteredData,
    ));
  }

  /// مسح جميع الفلاتر
  void clearAllFilters() {
    final currentState = state;
    if (currentState is! PestsDiseasesLoaded) return;

    emit(currentState.copyWith(
      searchQuery: '',
      selectedCategories: [],
      selectedSeverityLevels: [],
      filteredPestsAndDiseases: currentState.allPestsAndDiseases,
    ));
  }

  /// تبديل المفضلة
  void toggleFavorite(String pestId) {
    final currentState = state;
    if (currentState is! PestsDiseasesLoaded) return;

    final favorites = List<String>.from(currentState.favorites);
    
    if (favorites.contains(pestId)) {
      favorites.remove(pestId);
    } else {
      favorites.add(pestId);
    }

    emit(currentState.copyWith(favorites: favorites));
  }

  /// الحصول على قائمة المفضلة
  List<Map<String, dynamic>> getFavorites() {
    final currentState = state;
    if (currentState is! PestsDiseasesLoaded) return [];

    return currentState.allPestsAndDiseases
        .where((pest) => currentState.favorites.contains(pest['id']))
        .toList();
  }

  /// تطبيق الفلاتر على البيانات
  List<Map<String, dynamic>> _applyFilters({
    required List<Map<String, dynamic>> data,
    required String searchQuery,
    required List<String> selectedCategories,
    required List<String> selectedSeverityLevels,
  }) {
    return data.where((pest) {
      // فلترة البحث النصي
      final matchesSearch = searchQuery.isEmpty ||
          pest['name'].toString().toLowerCase().contains(searchQuery.toLowerCase()) ||
          (pest['scientificName']?.toString().toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
          pest['description'].toString().toLowerCase().contains(searchQuery.toLowerCase());

      // فلترة الفئة
      final matchesCategory = selectedCategories.isEmpty ||
          selectedCategories.contains(pest['category']);

      // فلترة مستوى الخطورة
      final matchesSeverity = selectedSeverityLevels.isEmpty ||
          selectedSeverityLevels.contains(pest['severity']);

      return matchesSearch && matchesCategory && matchesSeverity;
    }).toList();
  }
}
