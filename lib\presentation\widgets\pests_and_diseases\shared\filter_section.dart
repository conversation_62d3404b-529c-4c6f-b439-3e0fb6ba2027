import 'package:flutter/material.dart';

import '../../../../core/constants/assets_colors.dart';
import '../../../../core/constants/assets_fonts.dart';

/// قسم الفلترة للآفات والأمراض - مكون تشاركي
///
/// يوفر خيارات فلترة متقدمة للآفات والأمراض
class PestDiseaseFilterSection extends StatelessWidget {
  /// الفئات المتاحة للفلترة
  final List<String> categories;

  /// مستويات الخطورة المتاحة
  final List<String> severityLevels;

  /// الفئات المختارة
  final List<String> selectedCategories;

  /// مستويات الخطورة المختارة
  final List<String> selectedSeverityLevels;

  /// دالة عند تغيير الفئة
  final ValueChanged<String>? onCategoryChanged;

  /// دالة عند تغيير مستوى الخطورة
  final ValueChanged<String>? onSeverityChanged;

  /// دالة مسح جميع الفلاتر
  final VoidCallback? onClearAll;

  /// إنشاء قسم الفلترة
  const PestDiseaseFilterSection({
    super.key,
    required this.categories,
    required this.severityLevels,
    required this.selectedCategories,
    required this.selectedSeverityLevels,
    this.onCategoryChanged,
    this.onSeverityChanged,
    this.onClearAll,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس القسم
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'تصفية النتائج',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.primary,
                ),
              ),
              if (onClearAll != null && _hasActiveFilters())
                TextButton(
                  onPressed: onClearAll,
                  child: Text(
                    'مسح الكل',
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 14,
                      color: Colors.red,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // فلترة الفئات
          _buildFilterGroup(
            title: 'نوع الآفة/المرض',
            items: categories,
            selectedItems: selectedCategories,
            onItemChanged: onCategoryChanged,
            color: Colors.blue,
          ),
          
          const SizedBox(height: 16),
          
          // فلترة مستوى الخطورة
          _buildFilterGroup(
            title: 'مستوى الخطورة',
            items: severityLevels,
            selectedItems: selectedSeverityLevels,
            onItemChanged: onSeverityChanged,
            color: Colors.orange,
          ),
        ],
      ),
    );
  }

  /// بناء مجموعة فلاتر
  Widget _buildFilterGroup({
    required String title,
    required List<String> items,
    required List<String> selectedItems,
    required ValueChanged<String>? onItemChanged,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: items.map((item) {
            final isSelected = selectedItems.contains(item);
            return FilterChip(
              label: Text(
                item,
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 12,
                  color: isSelected ? Colors.white : color,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                onItemChanged?.call(item);
              },
              backgroundColor: Colors.grey[100],
              selectedColor: color,
              checkmarkColor: Colors.white,
              side: BorderSide(
                color: isSelected ? color : color.withValues(alpha: 0.3),
                width: 1,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// التحقق من وجود فلاتر نشطة
  bool _hasActiveFilters() {
    return selectedCategories.isNotEmpty || selectedSeverityLevels.isNotEmpty;
  }
}
