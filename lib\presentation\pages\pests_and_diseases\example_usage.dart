import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../bloc/agricultural_crops/crops_cubit.dart';
import 'simple_pests_diseases_page.dart';

/// مثال على كيفية استخدام الصفحة المبسطة
///
/// يوضح هذا المثال كيفية تشغيل الصفحة المبسطة للآفات والأمراض
/// مع إعداد Cubit المطلوب
class PestsDiseasesExample extends StatelessWidget {
  const PestsDiseasesExample({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'نظام إدارة صحة المحاصيل المبسط',
      debugShowCheckedModeBanner: false,
      
      // إعداد اللغة العربية والاتجاه من اليمين لليسار
      locale: const Locale('ar', 'AE'),
      textDirection: TextDirection.rtl,
      
      // إعداد الثيم
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Cairo',
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontFamily: 'Cairo'),
          bodyMedium: TextStyle(fontFamily: 'Cairo'),
          bodySmall: TextStyle(fontFamily: 'Cairo'),
        ),
      ),
      
      // الصفحة الرئيسية
      home: BlocProvider(
        create: (context) => CropsCubit(),
        child: const SimplePestsDiseasesPage(),
      ),
    );
  }
}

/// تطبيق تجريبي لاختبار الصفحة المبسطة
void main() {
  runApp(const PestsDiseasesExample());
}

/// مثال على كيفية التنقل إلى الصفحة المبسطة من صفحة أخرى
class NavigationExample extends StatelessWidget {
  const NavigationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال التنقل'),
        backgroundColor: Colors.green,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'مرحباً بك في نظام إدارة صحة المحاصيل',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            
            // زر للانتقال إلى الصفحة المبسطة
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => CropsCubit(),
                      child: const SimplePestsDiseasesPage(),
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.eco),
              label: const Text('إدارة صحة المحاصيل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // معلومات إضافية
            Container(
              margin: const EdgeInsets.all(20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الميزات المتاحة:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text('• بحث سريع في الآفات والأمراض'),
                  Text('• تشخيص بالصورة'),
                  Text('• حفظ المفضلة'),
                  Text('• عرض تفاصيل شاملة'),
                  Text('• واجهة بسيطة وسهلة'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// مثال على كيفية دمج الصفحة في تطبيق موجود
class IntegrationExample {
  /// دالة مساعدة للانتقال إلى صفحة إدارة صحة المحاصيل
  static void navigateToPestsManagement(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => CropsCubit(),
          child: const SimplePestsDiseasesPage(),
        ),
      ),
    );
  }
  
  /// دالة مساعدة للانتقال إلى صفحة التشخيص مباشرة
  static void navigateToQuickDiagnosis(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => CropsCubit(),
          child: const SimplePestsDiseasesPage(),
        ),
      ),
    );
  }
}

/// ويدجت بطاقة سريعة للوصول لإدارة صحة المحاصيل
class QuickAccessCard extends StatelessWidget {
  const QuickAccessCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: InkWell(
        onTap: () => IntegrationExample.navigateToPestsManagement(context),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.eco,
                size: 48,
                color: Colors.green[600],
              ),
              const SizedBox(height: 8),
              const Text(
                'صحة المحاصيل',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'إدارة الآفات والأمراض',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
