import 'package:flutter/material.dart';
import '../../../../../core/constants/index.dart';
import '../../../../../data/models/education/index.dart';

/// ويدجت بطاقة السؤال
///
/// يعرض هذا الويدجت سؤال الاختبار مع خيارات الإجابة
/// وفقاً للتفضيلات الـ18
class QuizQuestionCard extends StatelessWidget {
  /// نموذج السؤال
  final QuizQuestionModel question;

  /// رقم السؤال (يبدأ من 1)
  final int questionNumber;

  /// الخيار المختار (null إذا لم يتم الاختيار)
  final int? selectedOption;

  /// هل يتم عرض التغذية الراجعة؟
  final bool showFeedback;

  /// دالة يتم استدعاؤها عند اختيار خيار
  final Function(int) onOptionSelected;

  /// إنشاء ويدجت بطاقة السؤال
  ///
  /// المعلمات:
  /// - [question]: نموذج السؤال
  /// - [questionNumber]: رقم السؤال (يبدأ من 1)
  /// - [selectedOption]: الخيار المختار (null إذا لم يتم الاختيار)
  /// - [showFeedback]: هل يتم عرض التغذية الراجعة؟
  /// - [onOptionSelected]: دالة يتم استدعاؤها عند اختيار خيار
  const QuizQuestionCard({
    super.key,
    required this.question,
    required this.questionNumber,
    this.selectedOption,
    this.showFeedback = false,
    required this.onOptionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AssetsColors.kWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AssetsColors.primary.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
            // رأس السؤال
            Row(
              children: [
                // رقم السؤال
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AssetsColors.primary,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      '$questionNumber',
                      style: TextStyles.of(context).bodyMedium(
                        fontSize: 14,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: AssetsColors.kWhite,
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // نص "السؤال"
                Text(
                  'السؤال $questionNumber',
                  style: TextStyles.of(context).bodyMedium(
                    fontSize: 14,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.w600,
                    color: AssetsColors.primary,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // نص السؤال
            Text(
              question.question,
              style: TextStyles.of(context).headlineMedium(
                fontSize: 18,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.w600,
                color: Colors.black,
                height: 1.5,
              ),
              maxLines: 4,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 20),

            // خيارات الإجابة
            ...question.options.asMap().entries.map((entry) {
              final optionIndex = entry.key;
              final optionText = entry.value;

              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildOptionButton(context, optionIndex, optionText),
              );
            }),

            // التغذية الراجعة (إذا كانت مفعلة)
            if (showFeedback && selectedOption != null) ...[
              const SizedBox(height: 12),
              _buildFeedback(context),
            ],
          ],
        ),
      ),
    ),
    );
  }

  /// بناء زر الخيار
  Widget _buildOptionButton(
    BuildContext context,
    int optionIndex,
    String optionText,
  ) {
    final isSelected = selectedOption == optionIndex;
    final isCorrect = optionIndex == question.correctOptionIndex;
    final isWrong = showFeedback && isSelected && !isCorrect;

    // تحديد لون الزر
    Color backgroundColor;
    Color borderColor;
    Color textColor;

    if (showFeedback) {
      if (isCorrect) {
        // الإجابة الصحيحة - أخضر
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        borderColor = Colors.green;
        textColor = Colors.green.shade700;
      } else if (isWrong) {
        // الإجابة الخاطئة المختارة - أحمر
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        borderColor = Colors.red;
        textColor = Colors.red.shade700;
      } else {
        // خيار غير مختار
        backgroundColor = AssetsColors.kGrey50;
        borderColor = AssetsColors.kGrey100;
        textColor = AssetsColors.kGrey100;
      }
    } else {
      if (isSelected) {
        // مختار ولكن بدون تغذية راجعة
        backgroundColor = AssetsColors.primary.withValues(alpha: 0.1);
        borderColor = AssetsColors.primary;
        textColor = AssetsColors.primary;
      } else {
        // غير مختار - حدود مرئية
        backgroundColor = AssetsColors.kWhite;
        borderColor = Colors.grey.shade300;
        textColor = Colors.black;
      }
    }

    return GestureDetector(
      onTap: showFeedback ? null : () => onOptionSelected(optionIndex),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: borderColor,
            width: isSelected || (showFeedback && isCorrect) ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // رقم الخيار
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: borderColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  String.fromCharCode(65 + optionIndex), // A, B, C, D
                  style: TextStyles.of(context).bodySmall(
                    fontSize: 12,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.kWhite,
                  ),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // نص الخيار
            Expanded(
              child: Text(
                optionText,
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 14,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.w500,
                  color: textColor,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // أيقونة الحالة (في حالة التغذية الراجعة)
            if (showFeedback) ...[
              const SizedBox(width: 8),
              if (isCorrect)
                Icon(Icons.check_circle, color: Colors.green, size: 20)
              else if (isWrong)
                Icon(Icons.cancel, color: Colors.red, size: 20),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء التغذية الراجعة
  Widget _buildFeedback(BuildContext context) {
    final isCorrect = selectedOption == question.correctOptionIndex;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            isCorrect
                ? Colors.green.withOpacity(0.1)
                : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCorrect ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس التغذية الراجعة
          Row(
            children: [
              Icon(
                isCorrect ? Icons.check_circle : Icons.cancel,
                color: isCorrect ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                isCorrect ? 'إجابة صحيحة!' : 'إجابة خاطئة',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 14,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color:
                      isCorrect ? Colors.green.shade700 : Colors.red.shade700,
                ),
              ),
            ],
          ),

          // الشرح (إذا كان متوفراً)
          if (question.explanation != null &&
              question.explanation!.isNotEmpty) ...[
            const SizedBox(height: 6),
            Text(
              question.explanation!,
              style: TextStyles.of(context).bodyMedium(
                fontSize: 13,
                fontFamily: AssetsFonts.cairo,
                color: AssetsColors.kGrey100,
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}
