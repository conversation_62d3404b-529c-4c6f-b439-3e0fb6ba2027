import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/data/models/education/article_model.dart';
import 'package:agriculture/presentation/widgets/shared/cachd_net_image.dart';
import 'package:flutter/material.dart';
import 'package:jiffy/jiffy.dart';

import '../../../core/constants/text_styles.dart';

/// بطاقة المقالة التعليمية
///
/// تعرض هذه البطاقة معلومات المقالة التعليمية
class ArticleCard extends StatelessWidget {
  /// نموذج المقالة التعليمية
  final ArticleModel article;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback onTap;

  /// إنشاء بطاقة المقالة التعليمية
  ///
  /// المعلمات:
  /// - [article]: نموذج المقالة التعليمية
  /// - [onTap]: دالة يتم استدعاؤها عند النقر على البطاقة
  const ArticleCard({super.key, required this.article, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        decoration: BoxDecoration(
          color: AssetsColors.kWhite,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AssetsColors.primary.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          children: [
            // صورة المقالة
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
              child: SizedBox(
                width: 120,
                height: 120,
                child: CachedNetImage(
                  imageUrl: article.imageUrl,
                  fit: BoxFit.cover,
                ),
              ),
            ),

            // معلومات المقالة
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان المقالة
                    Text(
                      article.title,
                      style: TextStyles.of(context).headlineMedium(
                        fontSize: 16,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: AssetsColors.primary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // محتوى مختصر من المقالة
                    Text(
                      article.content.length > 100
                        ? '${article.content.substring(0, 100)}...'
                        : article.content,
                      style: TextStyles.of(context).bodyMedium(
                        fontSize: 14,
                        fontFamily: AssetsFonts.cairo,
                        color: AssetsColors.kGrey100,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // معلومات إضافية
                    Row(
                      children: [
                        // تاريخ النشر
                        Expanded(
                          flex: 2,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 12,
                                color: AssetsColors.primary.withValues(alpha: 0.7),
                              ),
                              const SizedBox(width: 3),
                              Flexible(
                                child: Text(
                                  _formatDate(article.publishDate),
                                  style: TextStyles.of(context).bodySmall(
                                    fontSize: 11,
                                    fontFamily: AssetsFonts.cairo,
                                    color: AssetsColors.kGrey100,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(width: 6),

                        // نوع المحتوى
                        Expanded(
                          flex: 1,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Icon(
                                Icons.article,
                                size: 12,
                                color: AssetsColors.primary.withValues(alpha: 0.7),
                              ),
                              const SizedBox(width: 3),
                              Text(
                                'مقالة',
                                style: TextStyles.of(context).bodySmall(
                                  fontSize: 11,
                                  fontFamily: AssetsFonts.cairo,
                                  color: AssetsColors.kGrey100,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return Jiffy.parseFromDateTime(date).fromNow();
  }
}
