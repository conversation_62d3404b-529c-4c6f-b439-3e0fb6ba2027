
import 'package:agriculture/data/models/education/lesson_model.dart';

import 'package:chewie/chewie.dart';

import 'package:video_player/video_player.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../imports.dart';

/// صفحة عرض فيديو الدرس
///
/// تعرض هذه الصفحة فيديو الدرس ومعلوماته
class LessonVideoPage extends StatefulWidget {
  /// نموذج الدرس
  final LessonModel lesson;

  /// إنشاء صفحة عرض فيديو الدرس
  ///
  /// المعلمات:
  /// - [lesson]: نموذج الدرس
  const LessonVideoPage({super.key, required this.lesson});

  @override
  State<LessonVideoPage> createState() => _LessonVideoPageState();
}

class _LessonVideoPageState extends State<LessonVideoPage> {
  /// تحكم مشغل الفيديو
  late VideoPlayerController _videoPlayerController;

  /// تحكم مشغل Chewie
  ChewieController? _chewieController;

  /// تحكم مشغل يوتيوب
  YoutubePlayerController? _youtubePlayerController;

  /// حالة تهيئة مشغل الفيديو
  bool _isInitialized = false;

  /// حالة وجود خطأ
  bool _hasError = false;

  /// رسالة الخطأ
  String _errorMessage = '';

  /// هل الفيديو من يوتيوب
  bool _isYoutubeVideo = false;

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayer();
  }

  /// تهيئة مشغل الفيديو
  Future<void> _initializeVideoPlayer() async {
    try {
      setState(() {
        _isInitialized = false;
        _hasError = false;
      });

      final videoUrl = widget.lesson.videoUrl;
      LoggerService.debug('تهيئة مشغل الفيديو مع الرابط: $videoUrl');

      // التحقق من نوع الفيديو
      if (videoUrl.contains('youtube.com') || videoUrl.contains('youtu.be')) {
        // فيديو يوتيوب
        _isYoutubeVideo = true;
        final videoId = YoutubePlayer.convertUrlToId(videoUrl);

        if (videoId != null) {
          LoggerService.debug('معرف فيديو يوتيوب: $videoId');

          _youtubePlayerController = YoutubePlayerController(
            initialVideoId: videoId,
            flags: const YoutubePlayerFlags(
              autoPlay: true,
              mute: false,
              enableCaption: true,
              captionLanguage: 'ar',
            ),
          );

          setState(() {
            _isInitialized = true;
            _hasError = false;
          });

          LoggerService.debug('تم تهيئة مشغل يوتيوب بنجاح');
        } else {
          throw Exception('لا يمكن استخراج معرف الفيديو من رابط يوتيوب');
        }
      } else {
        // فيديو عادي (MP4)
        _isYoutubeVideo = false;
        _videoPlayerController = VideoPlayerController.network(videoUrl);

        // انتظار تهيئة مشغل الفيديو
        await _videoPlayerController.initialize();

        // طباعة حالة التهيئة
        LoggerService.debug('تم تهيئة مشغل الفيديو: ${_videoPlayerController.value.isInitialized}');
        LoggerService.debug('مدة الفيديو: ${_videoPlayerController.value.duration}');

        _chewieController = ChewieController(
          videoPlayerController: _videoPlayerController,
          autoPlay: true,
          looping: false,
          aspectRatio: _videoPlayerController.value.aspectRatio,
          placeholder: const Center(child: CustomLoadingAnimation()),
          materialProgressColors: ChewieProgressColors(
            playedColor: AssetsColors.primary,
            handleColor: AssetsColors.primary,
            backgroundColor: AssetsColors.kGrey50,
            bufferedColor: AssetsColors.primary.withValues(alpha: 0.3),
          ),
          errorBuilder: (context, errorMessage) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                   Icon(
                    Icons.error_outline,
                    color: AssetsColors.error,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ أثناء تحميل الفيديو',
                    style: TextStyles.of(context).bodyMedium(
                      fontSize: 16,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                      color: AssetsColors.kGrey100,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    errorMessage,
                    style: TextStyles.of(context).bodyMedium(
                      fontSize: 14,
                      fontFamily: AssetsFonts.cairo,
                      color: AssetsColors.kGrey100,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _initializeVideoPlayer,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AssetsColors.primary,
                      foregroundColor: AssetsColors.kWhite,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'إعادة المحاولة',
                      style: TextStyles.of(context).bodyMedium(
                        fontSize: 14,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: AssetsColors.kWhite,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );

        // تحديث حالة الواجهة
        setState(() {
          _isInitialized = true;
          _hasError = false;
        });

        // طباعة تأكيد نجاح التهيئة
        LoggerService.debug('تم تهيئة مشغل الفيديو بنجاح');
      }
    } catch (e) {
      // طباعة الخطأ للتصحيح
      LoggerService.debug('خطأ في تهيئة مشغل الفيديو: $e');

      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  @override
  void dispose() {
    if (!_isYoutubeVideo) {
      _videoPlayerController.dispose();
      _chewieController?.dispose();
    }
    _youtubePlayerController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: defaultAppBar(
        color: AssetsColors.primary,
        context: context,
        titel: widget.lesson.title,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // مشغل الفيديو
              _buildVideoPlayer(),

              // معلومات الدرس
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان الدرس
                    Text(
                      widget.lesson.title,
                      style: TextStyles.of(context).headlineMedium(
                        fontSize: 18,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: AssetsColors.primary,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // وصف الدرس
                    Text(
                      widget.lesson.description,
                      style: TextStyles.of(context).bodyMedium(
                        fontSize: 16,
                        fontFamily: AssetsFonts.cairo,
                        color: AssetsColors.kGrey100,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // معلومات إضافية
                    _buildInfoCard(),

                    const SizedBox(height: 24),

                    // المحتوى الإضافي (إذا وجد)
                    if (widget.lesson.additionalContent != null &&
                        widget.lesson.additionalContent!.isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'محتوى إضافي',
                            style: TextStyles.of(context).headlineMedium(
                              fontSize: 16,
                              fontFamily: AssetsFonts.cairo,
                              fontWeight: FontWeight.bold,
                              color: AssetsColors.primary,
                            ),
                          ),

                          const SizedBox(height: 8),

                          Text(
                            widget.lesson.additionalContent!,
                            style: TextStyles.of(context).bodyMedium(
                              fontSize: 14,
                              fontFamily: AssetsFonts.cairo,
                              color: AssetsColors.kGrey100,
                            ),
                          ),

                          const SizedBox(height: 16), // مساحة إضافية في النهاية
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء مشغل الفيديو
  Widget _buildVideoPlayer() {
    if (_hasError) {
      return Container(
        width: double.infinity,
        height: 180, // تقليل الارتفاع قليلاً
        color: AssetsColors.kGrey50,
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.error_outline,
                  color: AssetsColors.error,
                  size: 40, // تقليل حجم الأيقونة
                ),
                const SizedBox(height: 12),
                Text(
                  'حدث خطأ أثناء تحميل الفيديو',
                  style: TextStyles.of(context).bodyMedium(
                    fontSize: 14, // تقليل حجم الخط
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.kGrey100,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                // عرض رسالة الخطأ
                Text(
                  _errorMessage,
                  style: TextStyles.of(context).bodyMedium(
                    fontSize: 12, // تقليل حجم الخط
                    fontFamily: AssetsFonts.cairo,
                    color: AssetsColors.kGrey100,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
                ElevatedButton(
                  onPressed: _initializeVideoPlayer,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AssetsColors.primary,
                    foregroundColor: AssetsColors.kWhite,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'إعادة المحاولة',
                    style: TextStyles.of(context).bodyMedium(
                      fontSize: 12,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                      color: AssetsColors.kWhite,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (!_isInitialized) {
      return Container(
        width: double.infinity,
        height: 180, // تقليل الارتفاع قليلاً
        color: AssetsColors.kGrey50,
        child: const Center(child: CustomLoadingAnimation()),
      );
    }

    // عرض مشغل الفيديو المناسب
    if (_isYoutubeVideo && _youtubePlayerController != null) {
      // عرض مشغل يوتيوب مع ارتفاع محدود
      LoggerService.debug('عرض مشغل يوتيوب');
      return SizedBox(
        width: double.infinity,
        height: 200, // ارتفاع ثابت لمشغل يوتيوب
        child: YoutubePlayer(
          controller: _youtubePlayerController!,
          showVideoProgressIndicator: true,
          progressIndicatorColor: AssetsColors.primary,
          progressColors: ProgressBarColors(
            playedColor: AssetsColors.primary,
            handleColor: AssetsColors.primary,
          ),
          onReady: () {
            LoggerService.debug('مشغل يوتيوب جاهز');
          },
          onEnded: (data) {
            LoggerService.debug('انتهى الفيديو');
          },
        ),
      );
    } else {
      // عرض مشغل الفيديو العادي مع ارتفاع محدود
      LoggerService.debug('عرض الفيديو: ${_videoPlayerController.value.isPlaying ? 'يعمل' : 'متوقف'}');
      LoggerService.debug('نسبة العرض: ${_videoPlayerController.value.aspectRatio}');

      return SizedBox(
        width: double.infinity,
        height: 200, // ارتفاع ثابت لمشغل الفيديو العادي
        child: AspectRatio(
          aspectRatio: _videoPlayerController.value.aspectRatio,
          child: Chewie(controller: _chewieController!),
        ),
      );
    }
  }

  /// بناء بطاقة معلومات الدرس
  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AssetsColors.kWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AssetsColors.primary.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // المدة
          _buildInfoRow(
            context,
            Icons.access_time,
            'المدة',
            '${widget.lesson.duration} دقيقة',
          ),

          const SizedBox(height: 12),

          // الترتيب
          _buildInfoRow(
            context,
            Icons.format_list_numbered,
            'الترتيب',
            'الدرس ${widget.lesson.order}',
          ),
        ],
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: AssetsColors.primary,
        ),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: TextStyles.of(context).bodyMedium(
            fontSize: 14,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: AssetsColors.kGrey100,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: TextStyles.of(context).bodyMedium(
            fontSize: 14,
            fontFamily: AssetsFonts.cairo,
            color: AssetsColors.kGrey100,
          ),
        ),
      ],
    );
  }
}
