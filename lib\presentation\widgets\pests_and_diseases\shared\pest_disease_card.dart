import 'package:flutter/material.dart';

import '../../../../core/constants/assets_colors.dart';
import '../../../../core/constants/assets_fonts.dart';

/// بطاقة عرض الآفة أو المرض - مكون تشاركي
///
/// يستخدم لعرض معلومات الآفة أو المرض بشكل موحد في جميع أنحاء التطبيق
class PestDiseaseCard extends StatelessWidget {
  /// اسم الآفة أو المرض
  final String name;

  /// الاسم العلمي
  final String? scientificName;

  /// وصف مختصر
  final String description;

  /// مستوى الخطورة
  final String severity;

  /// نوع الآفة/المرض
  final String category;

  /// دالة عند النقر على البطاقة
  final VoidCallback? onTap;

  /// دالة تبديل المفضلة
  final VoidCallback? onFavoriteToggle;

  /// هل العنصر في المفضلة
  final bool isFavorite;

  /// إنشاء بطاقة الآفة أو المرض
  const PestDiseaseCard({
    super.key,
    required this.name,
    this.scientificName,
    required this.description,
    required this.severity,
    required this.category,
    this.onTap,
    this.onFavoriteToggle,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة الآفة/المرض
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: _getSeverityColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getCategoryIcon(),
                  color: _getSeverityColor(),
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              
              // معلومات الآفة/المرض
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    if (scientificName != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        scientificName!,
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                    const SizedBox(height: 8),
                    Text(
                      description,
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getSeverityColor().withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        severity,
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 12,
                          color: _getSeverityColor(),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // زر المفضلة
              if (onFavoriteToggle != null)
                IconButton(
                  onPressed: onFavoriteToggle,
                  icon: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: isFavorite ? Colors.red : Colors.grey,
                  ),
                ),
              
              // سهم للتفاصيل
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على لون مستوى الخطورة
  Color _getSeverityColor() {
    switch (severity) {
      case 'عالي جداً':
      case 'عالي':
        return Colors.red;
      case 'متوسط':
        return Colors.orange;
      case 'منخفض':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة نوع الآفة/المرض
  IconData _getCategoryIcon() {
    switch (category) {
      case 'آفات':
      case 'حشرات':
        return Icons.bug_report;
      case 'أمراض':
      case 'فطريات':
        return Icons.local_hospital;
      case 'فيروسات':
        return Icons.coronavirus;
      default:
        return Icons.eco;
    }
  }
}
