
import '../../../imports.dart';

/// ويدجت رقاقة تصفية التصنيفات
///
/// يعرض تصنيف واحد كرقاقة قابلة للنقر لتصفية الدورات
class CategoryFilterChip extends StatelessWidget {
  /// معرف التصنيف
  final String categoryId;

  /// هل التصنيف محدد حالياً
  final bool isSelected;

  /// دالة استدعاء عند النقر
  final VoidCallback onTap;

  /// عدد الدورات في هذا التصنيف (اختياري)
  final int? coursesCount;

  /// إنشاء ويدجت رقاقة تصفية التصنيفات
  ///
  /// المعلمات:
  /// - [categoryId]: معرف التصنيف
  /// - [isSelected]: هل التصنيف محدد حالياً
  /// - [onTap]: دالة استدعاء عند النقر
  /// - [coursesCount]: عدد الدورات في هذا التصنيف (اختياري)
  const CategoryFilterChip({
    super.key,
    required this.categoryId,
    required this.isSelected,
    required this.onTap,
    this.coursesCount,
  });

  @override
  Widget build(BuildContext context) {
    final categoryName = EducationCategoriesConstants.getCategoryName(categoryId);
    final categoryColor = Color(EducationCategoriesConstants.getCategoryColor(categoryId));
    final iconCode = EducationCategoriesConstants.getCategoryIconCode(categoryId);

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.only(left: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? categoryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? categoryColor : categoryColor.withValues(alpha: 0.5),
            width: 1.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة التصنيف
            Icon(
              IconData(iconCode, fontFamily: 'MaterialIcons'),
              size: 18,
              color: isSelected ? AssetsColors.kWhite : categoryColor,
            ),

            const SizedBox(width: 6),

            // اسم التصنيف
            Text(
              categoryName,
              style: TextStyles.of(context).bodyMedium(
                fontSize: 14,
                fontFamily: AssetsFonts.cairo,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected ? AssetsColors.kWhite : categoryColor,
              ),
            ),

            // عدد الدورات (إذا توفر)
            if (coursesCount != null && coursesCount! > 0) ...[
              const SizedBox(width: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AssetsColors.kWhite.withOpacity(0.2)
                      : categoryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '$coursesCount',
                  style: TextStyles.of(context).bodySmall(
                    fontSize: 12,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                    color: isSelected ? AssetsColors.kWhite : categoryColor,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
