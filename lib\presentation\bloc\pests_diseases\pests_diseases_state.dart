import 'package:equatable/equatable.dart';

/// حالات Cubit إدارة الآفات والأمراض
///
/// تمثل جميع الحالات الممكنة لواجهة إدارة الآفات والأمراض
abstract class PestsDiseasesState extends Equatable {
  const PestsDiseasesState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class PestsDiseasesInitial extends PestsDiseasesState {
  const PestsDiseasesInitial();
}

/// حالة التحميل
class PestsDiseasesLoading extends PestsDiseasesState {
  const PestsDiseasesLoading();
}

/// حالة النجاح مع البيانات
class PestsDiseasesLoaded extends PestsDiseasesState {
  /// قائمة جميع الآفات والأمراض
  final List<Map<String, dynamic>> allPestsAndDiseases;

  /// قائمة الآفات والأمراض المفلترة
  final List<Map<String, dynamic>> filteredPestsAndDiseases;

  /// قائمة المفضلة
  final List<String> favorites;

  /// نص البحث الحالي
  final String searchQuery;

  /// الفئات المختارة للفلترة
  final List<String> selectedCategories;

  /// مستويات الخطورة المختارة
  final List<String> selectedSeverityLevels;

  /// إنشاء حالة النجاح
  const PestsDiseasesLoaded({
    required this.allPestsAndDiseases,
    required this.filteredPestsAndDiseases,
    required this.favorites,
    this.searchQuery = '',
    this.selectedCategories = const [],
    this.selectedSeverityLevels = const [],
  });

  /// نسخ الحالة مع تعديل بعض القيم
  PestsDiseasesLoaded copyWith({
    List<Map<String, dynamic>>? allPestsAndDiseases,
    List<Map<String, dynamic>>? filteredPestsAndDiseases,
    List<String>? favorites,
    String? searchQuery,
    List<String>? selectedCategories,
    List<String>? selectedSeverityLevels,
  }) {
    return PestsDiseasesLoaded(
      allPestsAndDiseases: allPestsAndDiseases ?? this.allPestsAndDiseases,
      filteredPestsAndDiseases: filteredPestsAndDiseases ?? this.filteredPestsAndDiseases,
      favorites: favorites ?? this.favorites,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCategories: selectedCategories ?? this.selectedCategories,
      selectedSeverityLevels: selectedSeverityLevels ?? this.selectedSeverityLevels,
    );
  }

  @override
  List<Object?> get props => [
        allPestsAndDiseases,
        filteredPestsAndDiseases,
        favorites,
        searchQuery,
        selectedCategories,
        selectedSeverityLevels,
      ];
}

/// حالة الخطأ
class PestsDiseasesError extends PestsDiseasesState {
  /// رسالة الخطأ
  final String message;

  /// إنشاء حالة الخطأ
  const PestsDiseasesError({required this.message});

  @override
  List<Object?> get props => [message];
}
