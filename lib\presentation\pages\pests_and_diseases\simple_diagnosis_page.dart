import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../data/models/pests_diseases_data.dart';

/// صفحة التشخيص البسيط للآفات والأمراض
///
/// تتيح للمستخدمين تشخيص الآفات والأمراض بطريقة بسيطة
/// باستخدام الكاميرا أو اختيار صورة من المعرض
class SimpleDiagnosisPage extends StatefulWidget {
  const SimpleDiagnosisPage({super.key});

  @override
  State<SimpleDiagnosisPage> createState() => _SimpleDiagnosisPageState();
}

class _SimpleDiagnosisPageState extends State<SimpleDiagnosisPage> {
  File? _selectedImage;
  List<Map<String, dynamic>> _diagnosisResults = [];
  bool _isAnalyzing = false;
  final ImagePicker _picker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'التشخيص البسيط',
        style: TextStyle(
          fontFamily: AssetsFonts.cairo,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: AssetsColors.primary,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildInstructionCard(),
          const SizedBox(height: 20),
          _buildImageSection(),
          const SizedBox(height: 20),
          _buildActionButtons(),
          if (_diagnosisResults.isNotEmpty) ...[
            const SizedBox(height: 30),
            _buildResultsSection(),
          ],
        ],
      ),
    );
  }

  /// بناء بطاقة التعليمات
  Widget _buildInstructionCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AssetsColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AssetsColors.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: AssetsColors.primary),
              const SizedBox(width: 8),
              Text(
                'كيفية الاستخدام',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.primary,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '1. التقط صورة واضحة للنبات المصاب\n'
            '2. تأكد من وضوح الأعراض في الصورة\n'
            '3. اضغط على "تحليل الصورة" للحصول على النتائج\n'
            '4. راجع التوصيات المقترحة للعلاج',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الصورة
  Widget _buildImageSection() {
    return Container(
      height: 250,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: _selectedImage != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.file(
                _selectedImage!,
                fit: BoxFit.cover,
                width: double.infinity,
              ),
            )
          : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.add_a_photo,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'اختر صورة للتشخيص',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _pickImage(ImageSource.camera),
                icon: const Icon(Icons.camera_alt),
                label: Text(
                  'التقاط صورة',
                  style: TextStyle(fontFamily: AssetsFonts.cairo),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AssetsColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _pickImage(ImageSource.gallery),
                icon: const Icon(Icons.photo_library),
                label: Text(
                  'من المعرض',
                  style: TextStyle(fontFamily: AssetsFonts.cairo),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedImage != null)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isAnalyzing ? null : _analyzeImage,
              icon: _isAnalyzing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.search),
              label: Text(
                _isAnalyzing ? 'جاري التحليل...' : 'تحليل الصورة',
                style: TextStyle(fontFamily: AssetsFonts.cairo),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// بناء قسم النتائج
  Widget _buildResultsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نتائج التشخيص',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AssetsColors.primary,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _diagnosisResults.length,
          itemBuilder: (context, index) {
            final result = _diagnosisResults[index];
            return _buildResultCard(result, index + 1);
          },
        ),
      ],
    );
  }

  /// بناء بطاقة النتيجة
  Widget _buildResultCard(Map<String, dynamic> result, int rank) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: AssetsColors.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    '$rank',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  result['name'] ?? 'غير محدد',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            result['description'] ?? 'لا يوجد وصف متاح',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          if (result['treatment'] != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'العلاج المقترح:',
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                      color: Colors.green[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    result['treatment'],
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// اختيار صورة
  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _diagnosisResults.clear();
        });
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في اختيار الصورة: $e');
    }
  }

  /// تحليل الصورة
  Future<void> _analyzeImage() async {
    if (_selectedImage == null) return;

    setState(() {
      _isAnalyzing = true;
      _diagnosisResults.clear();
    });

    try {
      // محاكاة تحليل الصورة
      await Future.delayed(const Duration(seconds: 2));

      // الحصول على نتائج عشوائية من قاعدة البيانات
      final allPests = PestsDiseasesData.getAllPestsAndDiseases();
      allPests.shuffle();
      
      setState(() {
        _diagnosisResults = allPests.take(3).toList();
        _isAnalyzing = false;
      });

      _showSuccessSnackBar('تم التحليل بنجاح!');
    } catch (e) {
      setState(() {
        _isAnalyzing = false;
      });
      _showErrorSnackBar('خطأ في تحليل الصورة: $e');
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: Colors.red,
      ),
    );
  }
}
