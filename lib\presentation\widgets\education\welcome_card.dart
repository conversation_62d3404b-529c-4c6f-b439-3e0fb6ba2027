
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:flutter/material.dart';

/// بطاقة الترحيب في صفحة التعليم
///
/// تعرض هذه البطاقة رسالة ترحيب دافئة للمزارعين
/// مع تصميم زراعي جذاب ومشجع
class WelcomeCard extends StatelessWidget {
  /// إنشاء بطاقة الترحيب
  const WelcomeCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.shade400,
            Colors.green.shade600,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // أيقونة زراعية
          Icon(
            Icons.eco,
            size: 48,
            color: Colors.white,
          ),

          const SizedBox(height: 12),

          // رسالة الترحيب الرئيسية
          Text(
            'أهلاً بك في رحلة التعلم الزراعي',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // رسالة تشجيعية
          Text(
            'تعلم خطوة بخطوة لتصبح مزارعاً ناجحاً',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              color: Colors.white.withOpacity(0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
